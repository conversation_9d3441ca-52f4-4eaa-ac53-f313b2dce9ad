<template>
  <div class="p-6 max-w-4xl mx-auto">
    <h1 class="text-3xl font-bold mb-6">PrimeVue Components Test</h1>
    
    <!-- Buttons -->
    <div class="mb-8">
      <h2 class="text-xl font-semibold mb-4">Buttons</h2>
      <div class="flex gap-4 flex-wrap">
        <Button label="Primary" />
        <Button label="Secondary" severity="secondary" />
        <Button label="Success" severity="success" />
        <Button label="Warning" severity="warning" />
        <Button label="Danger" severity="danger" />
        <Button label="Info" severity="info" />
        <Button label="Help" severity="help" />
        <Button icon="pi pi-check" />
        <Button icon="pi pi-check" label="Icon Button" />
      </div>
    </div>

    <!-- Cards -->
    <div class="mb-8">
      <h2 class="text-xl font-semibold mb-4">Cards</h2>
      <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
        <Card>
          <template #title>Simple Card</template>
          <template #content>
            <p>This is a simple card with some content to test the styling.</p>
          </template>
        </Card>
        
        <Card>
          <template #header>
            <div class="bg-blue-500 h-32 flex items-center justify-center">
              <i class="pi pi-star text-white text-2xl"></i>
            </div>
          </template>
          <template #title>Card with Header</template>
          <template #content>
            <p>This card has a header section with an icon.</p>
          </template>
          <template #footer>
            <div class="flex gap-2">
              <Button label="Save" size="small" />
              <Button label="Cancel" severity="secondary" size="small" />
            </div>
          </template>
        </Card>
      </div>
    </div>

    <!-- Form Elements -->
    <div class="mb-8">
      <h2 class="text-xl font-semibold mb-4">Form Elements</h2>
      <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <label class="block mb-2 font-medium">Input Text</label>
          <InputText v-model="testInput" placeholder="Enter some text" class="w-full" />
        </div>
        
        <div>
          <label class="block mb-2 font-medium">Textarea</label>
          <Textarea v-model="testTextarea" placeholder="Enter some text" class="w-full" rows="3" />
        </div>
      </div>
    </div>

    <!-- Data Table -->
    <div class="mb-8">
      <h2 class="text-xl font-semibold mb-4">Data Table</h2>
      <Card>
        <template #content>
          <DataTable :value="testData" paginator :rows="5">
            <Column field="id" header="ID" sortable>
              <template #body="{ data }">
                <Badge :value="data.id" severity="info" />
              </template>
            </Column>
            <Column field="name" header="Name" sortable />
            <Column field="email" header="Email" sortable />
            <Column header="Actions">
              <template #body="{ data }">
                <div class="flex gap-2">
                  <Button icon="pi pi-pencil" size="small" severity="warning" text rounded />
                  <Button icon="pi pi-trash" size="small" severity="danger" text rounded />
                </div>
              </template>
            </Column>
          </DataTable>
        </template>
      </Card>
    </div>

    <!-- Toast Test -->
    <div class="mb-8">
      <h2 class="text-xl font-semibold mb-4">Toast Messages</h2>
      <div class="flex gap-4 flex-wrap">
        <Button label="Success Toast" @click="showSuccess" />
        <Button label="Info Toast" severity="info" @click="showInfo" />
        <Button label="Warning Toast" severity="warning" @click="showWarning" />
        <Button label="Error Toast" severity="danger" @click="showError" />
      </div>
    </div>

    <!-- Dialog Test -->
    <div class="mb-8">
      <h2 class="text-xl font-semibold mb-4">Dialog</h2>
      <Button label="Show Dialog" @click="showDialog = true" />
      
      <Dialog v-model:visible="showDialog" header="Test Dialog" :style="{ width: '450px' }">
        <p>This is a test dialog to verify PrimeVue styling is working correctly.</p>
        <template #footer>
          <Button label="Cancel" severity="secondary" @click="showDialog = false" />
          <Button label="OK" @click="showDialog = false" />
        </template>
      </Dialog>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { useToast } from 'primevue/usetoast'

const toast = useToast()

// Test data
const testInput = ref('')
const testTextarea = ref('')
const showDialog = ref(false)

const testData = ref([
  { id: 1, name: 'John Doe', email: '<EMAIL>' },
  { id: 2, name: 'Jane Smith', email: '<EMAIL>' },
  { id: 3, name: 'Bob Johnson', email: '<EMAIL>' },
  { id: 4, name: 'Alice Brown', email: '<EMAIL>' },
  { id: 5, name: 'Charlie Wilson', email: '<EMAIL>' },
  { id: 6, name: 'Diana Davis', email: '<EMAIL>' },
])

// Toast methods
const showSuccess = () => {
  toast.add({ severity: 'success', summary: 'Success', detail: 'Operation completed successfully', life: 3000 })
}

const showInfo = () => {
  toast.add({ severity: 'info', summary: 'Info', detail: 'This is an info message', life: 3000 })
}

const showWarning = () => {
  toast.add({ severity: 'warn', summary: 'Warning', detail: 'This is a warning message', life: 3000 })
}

const showError = () => {
  toast.add({ severity: 'error', summary: 'Error', detail: 'Something went wrong', life: 3000 })
}
</script>
