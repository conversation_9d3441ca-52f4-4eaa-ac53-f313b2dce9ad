<template>
  <div class="login-container">
    <div class="login-wrapper">
      <!-- Logo Section -->
      <div class="logo-section">
        <img src="@/assets/simperum.png" alt="Simperum Logo" class="logo-img" />
        <h1 class="logo-text">Sim<span class="highlight">perum.</span></h1>
        <p class="logo-subtitle">Pintu Gerbang Satu Data Perumahan Jawa Tengah</p>
      </div>

      <!-- Login Card -->
      <Card class="login-card">
        <template #content>
          <div class="login-content">
            <form class="login-form" @submit.prevent="handleLogin">
              <!-- Username Input -->
              <div class="form-group">
                <label for="username" class="form-label">Username</label>
                <InputText
                  id="username"
                  v-model="forms.username"
                  type="text"
                  placeholder="Masukkan username Anda"
                  class="w-full"
                  required
                />
              </div>

              <div v-if="loginMode === 'otp' || !fido2Supported">
                <!-- OTP Input -->
                <div class="form-group">
                  <label for="otp" class="form-label">OTP</label>
                  <InputOtp id="otp" v-model="forms.OTP" :length="6" />
                </div>

                <div>
                  <br />
                  <Button label="Kirim OTP" class="p-button-text w-full" @click="sendOtp" />
                  <br />
                  <Button
                    label="Login"
                    class="p-button-success w-full"
                    :loading="isLoading"
                    :disabled="!forms.OTP"
                    @click="submit"
                  />
                  <br />
                  <Button
                    v-if="fido2Supported"
                    label="Login Dengan Biometrik"
                    class="p-button-text p-button-success w-full"
                    @click="loginMode = 'biometric'"
                  />
                </div>
              </div>

              <div v-else>
                <Button
                  label="Login Biometrik"
                  icon="pi pi-fingerprint"
                  class="p-button-success w-full"
                  :loading="isFido2Loading"
                  @click="authenticateWithFIDO2"
                />
                <br />
                <Button
                  label="Login Dengan OTP"
                  class="p-button-text w-full"
                  @click="loginMode = 'otp'"
                />
              </div>
            </form>
          </div>
        </template>
      </Card>

      <!-- Back to Home -->
      <Button
        icon="pi pi-arrow-left"
        label="Kembali ke Beranda"
        class="back-button w-full"
        text
        @click="goHome"
      />
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { useToast } from 'primevue/usetoast'
import InputText from 'primevue/inputtext'
import InputOtp from 'primevue/inputotp'
import Button from 'primevue/button'
import Card from 'primevue/card'
import {
  startRegistration,
  startAuthentication,
  browserSupportsWebAuthn,
} from '@simplewebauthn/browser'

const router = useRouter()
const authStore = useAuthStore()
const toast = useToast()

// Form data
const forms = ref({
  username: '',
  OTP: '',
})
const loginMode = ref('biometric')
const isLoading = ref(false)
const isFido2Loading = ref(false)
const fido2Supported = ref(false)

onMounted(() => {
  const basics = localStorage.getItem('basics')
  localStorage.clear()
  if (basics) localStorage.setItem('basics', basics)
  sessionStorage.clear()

  // Check FIDO2/WebAuthn support
  if (basics) {
    const basicData = JSON.parse(basics)
    if (!basicData.hasBiometric) loginMode.value = 'otp'
    if (basicData.username) forms.value.username = basicData.username
  } else {
    loginMode.value = 'otp'
  }
  fido2Supported.value = browserSupportsWebAuthn()
  if (!fido2Supported.value) {
    console.warn('WebAuthn is not supported in this browser')
  }
})

const sendOtp = () => {
  if (!forms.value.username) {
    toast.add({
      severity: 'error',
      summary: 'Error',
      detail: 'Masukkan Email / Nomor Whatsapp',
      life: 5000,
    })
    return
  }

  authStore.sendOtp({
    _Username: forms.value.username,
    _Purpose: 'login',
  })
}

const submit = async () => {
  if (!forms.value.username) {
    toast.add({
      severity: 'error',
      summary: 'Error',
      detail: 'Masukkan Username',
      life: 5000,
    })
    return
  }

  if (!forms.value.OTP) {
    toast.add({
      severity: 'error',
      summary: 'Error',
      detail: 'Masukkan OTP',
      life: 5000,
    })
    return
  }

  isLoading.value = true
  try {
    const result = await authStore.loginWithOtp(forms.value)

    if (result.success) {
      let basics = localStorage.getItem('basics')
      basics = basics ? JSON.parse(basics) : {}
      basics.username = forms.value.username
      localStorage.setItem('basics', JSON.stringify(basics))

      router.push('/responsive-layout')
    } else {
      toast.add({
        severity: 'error',
        summary: 'Error',
        detail: result.message,
        life: 5000,
      })
    }
  } catch (error) {
    console.error('Login error:', error)
    toast.add({
      severity: 'error',
      summary: 'Error',
      detail: 'An unexpected error occurred. Please try again.',
      life: 5000,
    })
  } finally {
    isLoading.value = false
  }
}

const authenticateWithFIDO2 = async () => {
  if (!fido2Supported.value) {
    toast.add({
      severity: 'error',
      summary: 'Error',
      detail: 'Browser tidak mendukung WebAuthn',
      life: 5000,
    })
    return
  }

  if (!forms.value.username) {
    toast.add({
      severity: 'error',
      summary: 'Error',
      detail: 'Masukkan Username terlebih dahulu',
      life: 5000,
    })
    return
  }

  isFido2Loading.value = true

  try {
    const optionsResponse = await authStore.fido2AuthenticateBegin(forms.value.username)

    if (!optionsResponse.success) {
      toast.add({
        severity: 'error',
        summary: 'Error',
        detail: optionsResponse.message || 'Gagal memulai autentikasi',
        life: 5000,
      })
      return
    }

    const credential = await startAuthentication(optionsResponse.data)
    const verificationResponse = await authStore.fido2AuthenticateComplete({
      username: forms.value.username,
      credential,
    })

    if (verificationResponse.success && verificationResponse.data) {
      let basics = localStorage.getItem('basics')
      basics = basics ? JSON.parse(basics) : {}
      basics.username = forms.value.username
      basics.hasBiometric = true
      localStorage.setItem('basics', JSON.stringify(basics))

      router.push('/responsive-layout')
      toast.add({
        severity: 'success',
        summary: 'Success',
        detail: 'Login berhasil dengan biometrik!',
        life: 5000,
      })
    } else {
      toast.add({
        severity: 'error',
        summary: 'Error',
        detail: verificationResponse.message || 'Autentikasi gagal',
        life: 5000,
      })
    }
  } catch (error) {
    console.error('FIDO2 authentication error:', error)
    let errorMessage = 'Gagal autentikasi biometrik: ' + error.message

    if (error.name === 'NotAllowedError') {
      errorMessage = 'Autentikasi dibatalkan atau tidak diizinkan'
    } else if (error.name === 'NotSupportedError') {
      errorMessage = 'Perangkat tidak mendukung autentikasi biometrik'
    }

    toast.add({
      severity: 'error',
      summary: 'Error',
      detail: errorMessage,
      life: 5000,
    })
  } finally {
    isFido2Loading.value = false
  }
}

// Navigate back to home
const goHome = () => {
  router.push('/')
}
</script>

<style>
.p-inputotp {
  display: flex;
}
.p-inputotp-input {
  flex: 1;
}
.login-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #fff 0%, #f3f3f3 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  position: relative;
}

.login-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: url('https://external-content.duckduckgo.com/iu/?u=https%3A%2F%2Fimg.freepik.com%2Fpremium-photo%2Fphoto-modern-home-3d-design_763111-13992.jpg&f=1&nofb=1&ipt=b8464edcb1da416d3970d874bcae853ba9a46483c6707d597d686c787fd47103');
  background-size: cover;
  background-position: center;
  opacity: 0.1;
  z-index: 0;
}

.login-wrapper {
  position: relative;
  z-index: 1;
  width: 100%;
  max-width: 450px;
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.logo-section {
  text-align: center;
  color: #aaa;
  margin-bottom: 1rem;
}

.logo-img {
  height: 60px;
  width: auto;
  margin-bottom: 1rem;
  filter: brightness(0) invert(1);
}

.logo-text {
  font-size: 2.5rem;
  font-weight: 300;
  margin: 0;
  letter-spacing: 0.5px;
}

.highlight {
  font-weight: 500;
  color: #333;
}

.logo-subtitle {
  font-size: 1rem;
  opacity: 0.9;
  margin: 0.5rem 0 0 0;
  font-weight: 300;
}

.login-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

.login-content {
  padding: 2rem;
}

.login-header {
  text-align: center;
  margin-bottom: 2rem;
}

.login-title {
  font-size: 1.8rem;
  font-weight: 600;
  color: #333;
  margin: 0 0 0.5rem 0;
}

.login-subtitle {
  color: #666;
  margin: 0;
  font-size: 0.95rem;
}

.login-form {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.form-label {
  font-weight: 500;
  color: #333;
  font-size: 0.9rem;
}

.form-options {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: -0.5rem;
}

.remember-me {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.remember-label {
  font-size: 0.9rem;
  color: #666;
  cursor: pointer;
}

.forgot-password {
  color: #8bc34a;
  text-decoration: none;
  font-size: 0.9rem;
  font-weight: 500;
}

.forgot-password:hover {
  text-decoration: underline;
}

.divider-text {
  color: #999;
  font-size: 0.85rem;
  background: white;
  padding: 0 1rem;
}

.social-login {
  margin-top: 1rem;
}

.back-button {
  align-self: center;
  color: #333;
  font-weight: 500;
}

.back-button:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

/* Responsive Design */
@media (max-width: 768px) {
  .login-container {
    padding: 1rem;
  }

  .login-wrapper {
    max-width: 100%;
  }

  .login-content {
    padding: 1.5rem;
  }

  .logo-text {
    font-size: 2rem;
  }

  .login-title {
    font-size: 1.5rem;
  }
}

/* PrimeVue Component Overrides */
:deep(.p-inputtext) {
  border-radius: 8px;
  border: 1px solid #e0e0e0;
  padding: 0.75rem;
  font-size: 0.95rem;
  transition: all 0.3s ease;
}

:deep(.p-inputtext:focus) {
  border-color: #8bc34a;
  box-shadow: 0 0 0 2px rgba(139, 195, 74, 0.2);
}

:deep(.p-password) {
  width: 100%;
}

:deep(.p-password .p-inputtext) {
  border-radius: 8px 0 0 8px;
  width: 100%;
}

:deep(.p-password .p-password-toggle-mask) {
  border-radius: 0 8px 8px 0;
  border-left: none;
  border: 1px solid #e0e0e0;
}

:deep(.p-password .p-password-toggle-mask:hover) {
  border-color: #8bc34a;
}

:deep(.p-checkbox .p-checkbox-box) {
  border-radius: 4px;
  border-color: #e0e0e0;
}

:deep(.p-checkbox .p-checkbox-box.p-highlight) {
  background-color: #8bc34a;
  border-color: #8bc34a;
}

:deep(.p-card .p-card-content) {
  padding: 0;
}

:deep(.p-divider .p-divider-content) {
  background-color: white;
}
</style>
