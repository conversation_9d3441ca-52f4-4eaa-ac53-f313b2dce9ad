{"name": "client", "version": "0.0.0", "private": true, "type": "module", "scripts": {"start": "vite --host --port 8000", "dev": "vite", "build": "vite build", "preview": "vite preview", "lint": "eslint . --fix", "format": "prettier --write src/"}, "dependencies": {"@simplewebauthn/browser": "^9.0.1", "@vue-leaflet/vue-leaflet": "^0.10.1", "chart.js": "^4.4.3", "leaflet": "^1.9.4", "pinia": "^2.1.7", "primeicons": "^7.0.0", "primevue": "^3.53.1", "tailwindcss-primeui": "^0.3.2", "vue": "^3.4.29", "vue-chartjs": "^5.3.1", "vue-router": "^4.3.3"}, "devDependencies": {"@eslint/js": "^9.4.0", "@vitejs/plugin-vue": "^5.0.5", "@vitejs/plugin-vue-jsx": "^4.0.0", "@vue/eslint-config-prettier": "^9.0.0", "autoprefixer": "^10.4.19", "eslint": "^9.4.0", "eslint-plugin-vue": "^9.26.0", "globals": "^15.4.0", "postcss": "^8.4.38", "prettier": "^3.3.2", "tailwindcss": "^3.4.4", "vite": "^5.2.13", "vite-plugin-vue-devtools": "^7.2.1", "vue-eslint-parser": "^9.4.3"}}