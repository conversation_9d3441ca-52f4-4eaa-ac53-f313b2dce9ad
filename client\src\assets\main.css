@import './base.css';
@tailwind base;
@tailwind components;
@tailwind utilities;

/* Ensure PrimeVue components maintain their styling */
@layer base {

  /* Prevent Tailwind from overriding PrimeVue button styles */
  .p-button {
    @apply !border-0;
  }

  /* Ensure PrimeVue input styles are preserved */
  .p-inputtext {
    @apply !border !border-gray-300 !rounded;
  }
}

#app {
  width: 100%;
  margin: 0;
  padding: 0;
  font-weight: normal;
  overflow-x: hidden;
}

a {
  text-decoration: none;
  color: inherit;
  transition: color 0.3s ease;
}

button {
  cursor: pointer;
  border: none;
  background: none;
  font-family: inherit;
}

/* Remove the grid layout for all screen sizes */
@media (min-width: 1024px) {
  body {
    display: block;
  }

  #app {
    display: block;
    padding: 0;
  }
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: #888;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #555;
}