@import './base.css';
@tailwind base;
@tailwind components;
@tailwind utilities;



#app {
  width: 100%;
  margin: 0;
  padding: 0;
  font-weight: normal;
  overflow-x: hidden;
}

a {
  text-decoration: none;
  color: inherit;
  transition: color 0.3s ease;
}

/* Only apply button reset to non-PrimeVue buttons */
button:not(.p-button):not(.p-button-icon-only) {
  cursor: pointer;
  border: none;
  background: none;
  font-family: inherit;
}

/* Force PrimeVue button styling */
.p-button {
  display: inline-flex !important;
  cursor: pointer !important;
  user-select: none !important;
  align-items: center !important;
  vertical-align: bottom !important;
  text-align: center !important;
  overflow: hidden !important;
  position: relative !important;
  color: #ffffff !important;
  background: #3B82F6 !important;
  border: 1px solid #3B82F6 !important;
  padding: 0.5rem 1rem !important;
  font-size: 1rem !important;
  transition: background-color 0.2s, color 0.2s, border-color 0.2s, box-shadow 0.2s, outline-color 0.2s !important;
  border-radius: 6px !important;
}

.p-button:hover {
  background: #2563eb !important;
  border-color: #2563eb !important;
}

.p-button-secondary {
  background: #64748b !important;
  border-color: #64748b !important;
}

.p-button-success {
  background: #22c55e !important;
  border-color: #22c55e !important;
}

.p-button-warning {
  background: #f97316 !important;
  border-color: #f97316 !important;
}

.p-button-danger {
  background: #ef4444 !important;
  border-color: #ef4444 !important;
}

.p-button-info {
  background: #0ea5e9 !important;
  border-color: #0ea5e9 !important;
}

.p-button-help {
  background: #a855f7 !important;
  border-color: #a855f7 !important;
}

/* Remove the grid layout for all screen sizes */
@media (min-width: 1024px) {
  body {
    display: block;
  }

  #app {
    display: block;
    padding: 0;
  }
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: #888;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #555;
}